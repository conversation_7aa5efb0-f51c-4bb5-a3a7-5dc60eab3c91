databaseChangeLog:
  - logicalFilePath: 'changelog-wym-1.0.yml'
  - changeSet:
      id: wym-1753065540
      author: wym
      changes:
        - addColumn:
            tableName: sys_user
            columns:
              - column:
                  name: selection_management
                  type: tinyint(1)
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
                  remarks: 预选管理-沟通中权限，0：查看自己，1：查看全部
                  afterColumn: workbench_role_type

  - changeSet:
      id: wym-1753065600
      author: wym
      comment: 修改business_member_validity_flow表remark字段数据类型为varchar(500)
      changes:
        - modifyDataType:
            tableName: business_member_validity_flow
            columnName: remark
            newDataType: varchar(500)
